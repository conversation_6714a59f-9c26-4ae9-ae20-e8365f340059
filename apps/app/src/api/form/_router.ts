import { z } from "zod"

import { prisma } from "@/lib/prisma"
import { authenticatedProcedure, modoAuthenticatedProcedure, router } from "@/lib/server/trpc"
import { FormStatus, FormType, QuestionType } from "@prisma/client"
import { TRPCError } from "@trpc/server"

// Validation schemas
const questionOptionSchema = z.object({
  id: z.string(),
  label: z.string(),
  value: z.string(),
})

const conditionalLogicSchema = z.object({
  questionId: z.string(),
  operator: z.enum(["equals", "not_equals", "contains", "not_contains", "greater_than", "less_than"]),
  value: z.union([z.string(), z.number(), z.boolean()]),
})

const formQuestionSchema = z.object({
  id: z.string().optional(),
  title: z.string().min(1, "Question title is required"),
  description: z.string().optional(),
  type: z.nativeEnum(QuestionType),
  order: z.number().int().min(0),
  isRequired: z.boolean().default(false),
  minLength: z.number().int().min(0).optional(),
  maxLength: z.number().int().min(1).optional(),
  minValue: z.number().optional(),
  maxValue: z.number().optional(),
  options: z.array(questionOptionSchema).optional(),
  showConditions: z.array(conditionalLogicSchema).optional(),
})

const createFormSchema = z.object({
  title: z.string().min(1, "Form title is required"),
  description: z.string().optional(),
  type: z.nativeEnum(FormType),
  showProgressBar: z.boolean().default(true),
  allowSaveProgress: z.boolean().default(false),
  requireAuth: z.boolean().default(true),
  enableConditionalLogic: z.boolean().default(false),
  questions: z.array(formQuestionSchema).default([]),
})

const updateFormSchema = createFormSchema.partial().extend({
  id: z.string(),
})

const formQuerySchema = z.object({
  page: z.number().int().min(1).default(1),
  pageSize: z.number().int().min(1).max(100).default(10),
  type: z.nativeEnum(FormType).optional(),
  status: z.nativeEnum(FormStatus).optional(),
  search: z.string().optional(),
})

export const formRouter = router({
  // Get all forms with pagination and filtering
  getAll: modoAuthenticatedProcedure.input(formQuerySchema).query(async ({ input }) => {
    const { page, pageSize, type, status, search } = input
    const skip = (page - 1) * pageSize

    const where = {
      ...(type && { type }),
      ...(status && { status }),
      ...(search && {
        OR: [
          { title: { contains: search, mode: "insensitive" as const } },
          { description: { contains: search, mode: "insensitive" as const } },
        ],
      }),
    }

    const [forms, total] = await Promise.all([
      prisma.form.findMany({
        where,
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          questions: {
            orderBy: { order: "asc" },
            select: {
              id: true,
              title: true,
              type: true,
              order: true,
              isRequired: true,
            },
          },
          _count: {
            select: {
              submissions: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: pageSize,
      }),
      prisma.form.count({ where }),
    ])

    return {
      data: forms,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize),
      },
    }
  }),

  // Get form by ID with full details
  getById: modoAuthenticatedProcedure.input(z.string()).query(async ({ input: id }) => {
    const form = await prisma.form.findUnique({
      where: { id },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        questions: {
          orderBy: { order: "asc" },
        },
        _count: {
          select: {
            submissions: true,
          },
        },
      },
    })

    if (!form) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Form not found",
      })
    }

    return form
  }),

  // Get active form by type (for public use)
  getActiveByType: authenticatedProcedure.input(z.nativeEnum(FormType)).query(async ({ input: type }) => {
    const form = await prisma.form.findFirst({
      where: {
        type,
        status: FormStatus.ACTIVE,
        isActive: true,
      },
      include: {
        questions: {
          orderBy: { order: "asc" },
          select: {
            id: true,
            title: true,
            description: true,
            type: true,
            order: true,
            isRequired: true,
            minLength: true,
            maxLength: true,
            minValue: true,
            maxValue: true,
            options: true,
            showConditions: true,
          },
        },
      },
    })

    return form
  }),

  // Create new form
  create: modoAuthenticatedProcedure.input(createFormSchema).mutation(async ({ ctx, input }) => {
    const userId = ctx.session?.user.id

    if (!userId) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "User not authenticated",
      })
    }

    const { questions, ...formData } = input

    // Enhanced validation
    if (!formData.title.trim()) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "Form title is required",
      })
    }

    // Validate questions
    for (const question of questions) {
      if (!question.title.trim()) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "All questions must have a title",
        })
      }

      // Validate choice questions have options
      if (question.type === "SINGLE_CHOICE" || question.type === "MULTIPLE_CHOICE") {
        if (!question.options || question.options.length < 2) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `Question "${question.title}" must have at least 2 options`,
          })
        }

        // Validate option labels
        for (const option of question.options) {
          if (!option.label.trim()) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: `All options in question "${question.title}" must have labels`,
            })
          }
        }
      }

      // Validate number/rating ranges
      if (question.type === "NUMBER" || question.type === "RATING") {
        if (question.minValue !== undefined && question.maxValue !== undefined) {
          if (question.minValue >= question.maxValue) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: `Question "${question.title}": minimum value must be less than maximum value`,
            })
          }
        }
      }

      // Validate text length constraints
      if (question.type === "TEXT_SHORT" || question.type === "TEXT_LONG") {
        if (question.minLength !== undefined && question.maxLength !== undefined) {
          if (question.minLength >= question.maxLength) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: `Question "${question.title}": minimum length must be less than maximum length`,
            })
          }
        }
      }
    }

    return await prisma.$transaction(async (tx) => {
      // Create the form
      const form = await tx.form.create({
        data: {
          ...formData,
          createdBy: userId,
        },
      })

      // Create questions if provided
      if (questions.length > 0) {
        await tx.formQuestion.createMany({
          data: questions.map((question) => ({
            ...question,
            formId: form.id,
            options: question.options ? JSON.stringify(question.options) : null,
            showConditions: question.showConditions ? JSON.stringify(question.showConditions) : null,
          })),
        })
      }

      return form
    })
  }),

  // Update form
  update: modoAuthenticatedProcedure.input(updateFormSchema).mutation(async ({ input }) => {
    const { id, questions, ...formData } = input

    const existingForm = await prisma.form.findUnique({
      where: { id },
      include: { questions: true },
    })

    if (!existingForm) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Form not found",
      })
    }

    return await prisma.$transaction(async (tx) => {
      // Update form data
      const updatedForm = await tx.form.update({
        where: { id },
        data: formData,
      })

      // Handle questions update if provided
      if (questions) {
        // Delete existing questions
        await tx.formQuestion.deleteMany({
          where: { formId: id },
        })

        // Create new questions
        if (questions.length > 0) {
          await tx.formQuestion.createMany({
            data: questions.map((question) => ({
              ...question,
              formId: id,
              options: question.options ? JSON.stringify(question.options) : null,
              showConditions: question.showConditions ? JSON.stringify(question.showConditions) : null,
            })),
          })
        }
      }

      return updatedForm
    })
  }),

  // Delete form
  delete: modoAuthenticatedProcedure.input(z.string()).mutation(async ({ input: id }) => {
    const form = await prisma.form.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            submissions: true,
          },
        },
      },
    })

    if (!form) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Form not found",
      })
    }

    // Prevent deletion if form has submissions
    if (form._count.submissions > 0) {
      throw new TRPCError({
        code: "PRECONDITION_FAILED",
        message: "Cannot delete form with existing submissions. Archive it instead.",
      })
    }

    await prisma.form.delete({
      where: { id },
    })

    return { success: true }
  }),

  // Activate form (deactivates other forms of same type)
  activate: modoAuthenticatedProcedure.input(z.string()).mutation(async ({ input: id }) => {
    const form = await prisma.form.findUnique({
      where: { id },
    })

    if (!form) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Form not found",
      })
    }

    return await prisma.$transaction(async (tx) => {
      // Deactivate all other forms of the same type
      await tx.form.updateMany({
        where: {
          type: form.type,
          id: { not: id },
        },
        data: {
          isActive: false,
          status: FormStatus.INACTIVE,
        },
      })

      // Activate the target form
      const activatedForm = await tx.form.update({
        where: { id },
        data: {
          isActive: true,
          status: FormStatus.ACTIVE,
        },
      })

      return activatedForm
    })
  }),

  // Deactivate form
  deactivate: modoAuthenticatedProcedure.input(z.string()).mutation(async ({ input: id }) => {
    const form = await prisma.form.update({
      where: { id },
      data: {
        isActive: false,
        status: FormStatus.INACTIVE,
      },
    })

    return form
  }),

  // Form submission endpoints
  submission: router({
    // Submit form response
    submit: authenticatedProcedure
      .input(
        z.object({
          formId: z.string(),
          subscriptionId: z.string().optional(),
          responses: z.array(
            z.object({
              questionId: z.string(),
              textValue: z.string().optional(),
              numberValue: z.number().optional(),
              booleanValue: z.boolean().optional(),
              dateValue: z.date().optional(),
              selectedOptions: z.array(z.string()).optional(),
            })
          ),
          ipAddress: z.string().optional(),
          userAgent: z.string().optional(),
        })
      )
      .mutation(async ({ ctx, input }) => {
        const userId = ctx.session?.user.id

        if (!userId) {
          throw new TRPCError({
            code: "UNAUTHORIZED",
            message: "User not authenticated",
          })
        }

        const { formId, subscriptionId, responses, ipAddress, userAgent } = input

        // Verify form exists and is active
        const form = await prisma.form.findFirst({
          where: {
            id: formId,
            status: FormStatus.ACTIVE,
            isActive: true,
          },
          include: {
            questions: true,
          },
        })

        if (!form) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Form not found or not active",
          })
        }

        // Validate required questions are answered
        const requiredQuestions = form.questions.filter((q) => q.isRequired)
        const answeredQuestionIds = responses.map((r) => r.questionId)
        const missingRequired = requiredQuestions.filter((q) => !answeredQuestionIds.includes(q.id))

        if (missingRequired.length > 0) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `Missing required questions: ${missingRequired.map((q) => q.title).join(", ")}`,
          })
        }

        return await prisma.$transaction(async (tx) => {
          // Create submission
          const submission = await tx.formSubmission.create({
            data: {
              formId,
              userId,
              subscriptionId,
              isComplete: true,
              submittedAt: new Date(),
              ipAddress,
              userAgent,
            },
          })

          // Create responses
          await tx.formResponse.createMany({
            data: responses.map((response) => ({
              submissionId: submission.id,
              questionId: response.questionId,
              textValue: response.textValue,
              numberValue: response.numberValue,
              booleanValue: response.booleanValue,
              dateValue: response.dateValue,
              selectedOptions: response.selectedOptions ? JSON.stringify(response.selectedOptions) : null,
            })),
          })

          return submission
        })
      }),

    // Get submissions for a form (admin only)
    getByForm: modoAuthenticatedProcedure
      .input(
        z.object({
          formId: z.string(),
          page: z.number().int().min(1).default(1),
          pageSize: z.number().int().min(1).max(100).default(10),
        })
      )
      .query(async ({ input }) => {
        const { formId, page, pageSize } = input
        const skip = (page - 1) * pageSize

        const [submissions, total] = await Promise.all([
          prisma.formSubmission.findMany({
            where: { formId },
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
              subscription: {
                select: {
                  id: true,
                  status: true,
                  plan: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
              responses: {
                include: {
                  question: {
                    select: {
                      id: true,
                      title: true,
                      type: true,
                    },
                  },
                },
              },
            },
            orderBy: { submittedAt: "desc" },
            skip,
            take: pageSize,
          }),
          prisma.formSubmission.count({ where: { formId } }),
        ])

        return {
          data: submissions,
          pagination: {
            page,
            pageSize,
            total,
            totalPages: Math.ceil(total / pageSize),
          },
        }
      }),

    // Get submission analytics
    getAnalytics: modoAuthenticatedProcedure
      .input(
        z.object({
          formId: z.string(),
          dateFrom: z.date().optional(),
          dateTo: z.date().optional(),
        })
      )
      .query(async ({ input }) => {
        const { formId, dateFrom, dateTo } = input

        const whereClause = {
          formId,
          isComplete: true,
          ...(dateFrom &&
            dateTo && {
              submittedAt: {
                gte: dateFrom,
                lte: dateTo,
              },
            }),
        }

        const [totalSubmissions, form] = await Promise.all([
          prisma.formSubmission.count({ where: whereClause }),
          prisma.form.findUnique({
            where: { id: formId },
            include: {
              questions: {
                orderBy: { order: "asc" },
              },
            },
          }),
        ])

        if (!form) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Form not found",
          })
        }

        // Get response analytics for each question
        const questionAnalytics = await Promise.all(
          form.questions.map(async (question) => {
            const responses = await prisma.formResponse.findMany({
              where: {
                questionId: question.id,
                submission: whereClause,
              },
            })

            const analytics: any = {
              questionId: question.id,
              questionTitle: question.title,
              questionType: question.type,
              totalResponses: responses.length,
            }

            // Type-specific analytics
            switch (question.type) {
              case "SINGLE_CHOICE":
              case "MULTIPLE_CHOICE":
                const optionCounts: Record<string, number> = {}
                responses.forEach((response) => {
                  if (response.selectedOptions) {
                    const options = JSON.parse(response.selectedOptions as string)
                    options.forEach((option: string) => {
                      optionCounts[option] = (optionCounts[option] || 0) + 1
                    })
                  }
                })
                analytics.optionCounts = optionCounts
                break

              case "YES_NO":
                const yesCount = responses.filter((r) => r.booleanValue === true).length
                const noCount = responses.filter((r) => r.booleanValue === false).length
                analytics.yesCount = yesCount
                analytics.noCount = noCount
                break

              case "RATING":
              case "NUMBER":
                const values = responses.map((r) => r.numberValue).filter((v) => v !== null) as number[]
                if (values.length > 0) {
                  analytics.average = values.reduce((a, b) => a + b, 0) / values.length
                  analytics.min = Math.min(...values)
                  analytics.max = Math.max(...values)
                }
                break

              case "TEXT_SHORT":
              case "TEXT_LONG":
                analytics.averageLength =
                  responses.map((r) => r.textValue?.length || 0).reduce((a, b) => a + b, 0) / responses.length
                break
            }

            return analytics
          })
        )

        return {
          totalSubmissions,
          questionAnalytics,
          form: {
            id: form.id,
            title: form.title,
            type: form.type,
          },
        }
      }),
  }),
})
