"use client"

import React, { useState } from "react"
import { motion } from "framer-motion"
import { Plus, Save, Eye, ArrowLeft } from "lucide-react"
import { toast } from "react-toastify"
import { useRouter } from "next/navigation"

import { trpc } from "@/lib/trpc/client"
import { <PERSON><PERSON> } from "@nextui-org/button"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Input } from "@nextui-org/input"
import { Textarea } from "@nextui-org/input"
import { Select, SelectItem } from "@nextui-org/select"
import { Switch } from "@nextui-org/switch"
import { Divider } from "@nextui-org/divider"
import { FormType, QuestionType } from "@prisma/client"

interface FormQuestion {
  id: string
  title: string
  description?: string
  type: QuestionType
  order: number
  isRequired: boolean
  minLength?: number
  maxLength?: number
  minValue?: number
  maxValue?: number
  options?: Array<{ id: string; label: string; value: string }>
}

interface FormData {
  title: string
  description?: string
  type: FormType
  showProgressBar: boolean
  allowSaveProgress: boolean
  requireAuth: boolean
  enableConditionalLogic: boolean
  questions: FormQuestion[]
}

export const FormBuilder: React.FC = () => {
  const router = useRouter()
  const [formData, setFormData] = useState<FormData>({
    title: "",
    description: "",
    type: FormType.GENERAL_FEEDBACK,
    showProgressBar: true,
    allowSaveProgress: false,
    requireAuth: true,
    enableConditionalLogic: false,
    questions: [],
  })

  const [isPreviewMode, setIsPreviewMode] = useState(false)

  const createForm = trpc.form.create.useMutation({
    onSuccess: () => {
      toast.success("Formulaire créé avec succès!")
      router.push("/admin/forms")
    },
    onError: (error) => {
      toast.error(error.message || "Erreur lors de la création du formulaire")
    },
  })

  const handleSave = () => {
    if (!formData.title.trim()) {
      toast.error("Le titre du formulaire est requis")
      return
    }

    createForm.mutate(formData)
  }

  const addQuestion = () => {
    const newQuestion: FormQuestion = {
      id: `question-${Date.now()}`,
      title: "Nouvelle question",
      type: QuestionType.TEXT_SHORT,
      order: formData.questions.length,
      isRequired: false,
    }

    setFormData(prev => ({
      ...prev,
      questions: [...prev.questions, newQuestion],
    }))
  }

  const updateQuestion = (questionId: string, updates: Partial<FormQuestion>) => {
    setFormData(prev => ({
      ...prev,
      questions: prev.questions.map(q =>
        q.id === questionId ? { ...q, ...updates } : q
      ),
    }))
  }

  const removeQuestion = (questionId: string) => {
    setFormData(prev => ({
      ...prev,
      questions: prev.questions.filter(q => q.id !== questionId),
    }))
  }

  const getQuestionTypeLabel = (type: QuestionType) => {
    switch (type) {
      case QuestionType.TEXT_SHORT:
        return "Texte court"
      case QuestionType.TEXT_LONG:
        return "Texte long"
      case QuestionType.EMAIL:
        return "Email"
      case QuestionType.NUMBER:
        return "Nombre"
      case QuestionType.RATING:
        return "Note"
      case QuestionType.YES_NO:
        return "Oui/Non"
      case QuestionType.SINGLE_CHOICE:
        return "Choix unique"
      case QuestionType.MULTIPLE_CHOICE:
        return "Choix multiple"
      case QuestionType.DATE:
        return "Date"
      default:
        return type
    }
  }

  if (isPreviewMode) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Button
            variant="light"
            startContent={<ArrowLeft className="w-4 h-4" />}
            onPress={() => setIsPreviewMode(false)}
          >
            Retour à l'édition
          </Button>
        </div>

        <Card>
          <CardHeader>
            <h3 className="text-xl font-semibold">{formData.title}</h3>
            {formData.description && (
              <p className="text-default-600">{formData.description}</p>
            )}
          </CardHeader>
          <CardBody className="space-y-6">
            {formData.questions.map((question, index) => (
              <div key={question.id} className="space-y-2">
                <h4 className="font-medium flex items-center gap-2">
                  {question.title}
                  {question.isRequired && (
                    <span className="text-danger text-sm">*</span>
                  )}
                </h4>
                {question.description && (
                  <p className="text-sm text-default-600">{question.description}</p>
                )}
                <div className="p-4 bg-default-100 rounded-lg">
                  <p className="text-sm text-default-500">
                    Type: {getQuestionTypeLabel(question.type)}
                  </p>
                </div>
              </div>
            ))}
          </CardBody>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <Button
          variant="light"
          startContent={<ArrowLeft className="w-4 h-4" />}
          onPress={() => router.push("/admin/forms")}
        >
          Retour aux formulaires
        </Button>

        <div className="flex items-center gap-2">
          <Button
            variant="light"
            startContent={<Eye className="w-4 h-4" />}
            onPress={() => setIsPreviewMode(true)}
          >
            Aperçu
          </Button>
          <Button
            color="primary"
            startContent={<Save className="w-4 h-4" />}
            onPress={handleSave}
            isLoading={createForm.isPending}
          >
            Enregistrer
          </Button>
        </div>
      </div>

      {/* Form Configuration */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Configuration du formulaire</h3>
        </CardHeader>
        <CardBody className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Titre du formulaire"
              placeholder="Entrez le titre..."
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              isRequired
            />

            <Select
              label="Type de formulaire"
              selectedKeys={[formData.type]}
              onSelectionChange={(keys) =>
                setFormData(prev => ({
                  ...prev,
                  type: Array.from(keys)[0] as FormType
                }))
              }
            >
              <SelectItem key={FormType.SUBSCRIPTION_CANCELLATION}>
                Annulation d'abonnement
              </SelectItem>
              <SelectItem key={FormType.GENERAL_FEEDBACK}>
                Feedback général
              </SelectItem>
              <SelectItem key={FormType.SUPPORT_REQUEST}>
                Demande de support
              </SelectItem>
            </Select>
          </div>

          <Textarea
            label="Description (optionnelle)"
            placeholder="Décrivez le but de ce formulaire..."
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          />

          <Divider />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Switch
              isSelected={formData.showProgressBar}
              onValueChange={(checked) =>
                setFormData(prev => ({ ...prev, showProgressBar: checked }))
              }
            >
              Afficher la barre de progression
            </Switch>

            <Switch
              isSelected={formData.requireAuth}
              onValueChange={(checked) =>
                setFormData(prev => ({ ...prev, requireAuth: checked }))
              }
            >
              Authentification requise
            </Switch>

            <Switch
              isSelected={formData.allowSaveProgress}
              onValueChange={(checked) =>
                setFormData(prev => ({ ...prev, allowSaveProgress: checked }))
              }
            >
              Permettre la sauvegarde
            </Switch>

            <Switch
              isSelected={formData.enableConditionalLogic}
              onValueChange={(checked) =>
                setFormData(prev => ({ ...prev, enableConditionalLogic: checked }))
              }
            >
              Logique conditionnelle
            </Switch>
          </div>
        </CardBody>
      </Card>

      {/* Questions */}
      <Card>
        <CardHeader className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">Questions</h3>
          <Button
            color="primary"
            size="sm"
            startContent={<Plus className="w-4 h-4" />}
            onPress={addQuestion}
          >
            Ajouter une question
          </Button>
        </CardHeader>
        <CardBody className="space-y-4">
          {formData.questions.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-default-500">Aucune question ajoutée</p>
              <Button
                color="primary"
                variant="light"
                onPress={addQuestion}
                className="mt-2"
              >
                Ajouter votre première question
              </Button>
            </div>
          ) : (
            formData.questions.map((question, index) => (
              <motion.div
                key={question.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-4 border border-default-200 rounded-lg space-y-4"
              >
                <div className="flex items-center justify-between">
                  <span className="text-sm text-default-500">Question {index + 1}</span>
                  <Button
                    size="sm"
                    color="danger"
                    variant="light"
                    onPress={() => removeQuestion(question.id)}
                  >
                    Supprimer
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Titre de la question"
                    value={question.title}
                    onChange={(e) => updateQuestion(question.id, { title: e.target.value })}
                  />

                  <Select
                    label="Type de question"
                    selectedKeys={[question.type]}
                    onSelectionChange={(keys) =>
                      updateQuestion(question.id, {
                        type: Array.from(keys)[0] as QuestionType
                      })
                    }
                  >
                    {Object.values(QuestionType).map(type => (
                      <SelectItem key={type}>
                        {getQuestionTypeLabel(type)}
                      </SelectItem>
                    ))}
                  </Select>
                </div>

                <Textarea
                  label="Description (optionnelle)"
                  value={question.description || ""}
                  onChange={(e) => updateQuestion(question.id, { description: e.target.value })}
                />

                <Switch
                  isSelected={question.isRequired}
                  onValueChange={(checked) =>
                    updateQuestion(question.id, { isRequired: checked })
                  }
                >
                  Question obligatoire
                </Switch>
              </motion.div>
            ))
          )}
        </CardBody>
      </Card>
    </div>
  )
}
