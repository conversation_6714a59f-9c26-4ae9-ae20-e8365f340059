"use client"

import React, { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { motion } from "framer-motion"
import { <PERSON>Left, Eye, Plus, Save } from "lucide-react"
import { toast } from "react-toastify"

import { trpc } from "@/lib/trpc/client"
import type { ShowConditions } from "@/types/conditional-logic"
import { Button } from "@nextui-org/button"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Input } from "@nextui-org/input"
import { Textarea } from "@nextui-org/input"
import { Select, SelectItem } from "@nextui-org/select"
import { Switch } from "@nextui-org/switch"
import { FormType, QuestionType } from "@prisma/client"

import { ConditionalLogicEditor } from "./conditional-logic-editor"

interface FormQuestion {
  id: string
  title: string
  description?: string
  type: QuestionType
  order: number
  isRequired: boolean
  minLength?: number
  maxLength?: number
  minValue?: number
  maxValue?: number
  options?: Array<{ id: string; label: string; value: string }>
  showConditions?: ShowConditions | null
}

interface FormData {
  title: string
  description?: string
  questions: FormQuestion[]
}

export const FormBuilder: React.FC = () => {
  const router = useRouter()
  const [formData, setFormData] = useState<FormData>({
    title: "",
    description: "",
    questions: [],
  })

  const [isPreviewMode, setIsPreviewMode] = useState(false)

  const createForm = trpc.form.create.useMutation({
    onSuccess: () => {
      toast.success("Formulaire créé avec succès!")
      router.push("/admin/forms")
    },
    onError: (error) => {
      toast.error(error.message || "Erreur lors de la création du formulaire")
    },
  })

  const handleSave = () => {
    if (!formData.title.trim()) {
      toast.error("Le titre du formulaire est requis")
      return
    }

    // Validate all questions
    for (const question of formData.questions) {
      const error = validateQuestion(question)
      if (error) {
        toast.error(`Erreur dans la question "${question.title}": ${error}`)
        return
      }
    }

    // Create form with hardcoded values for subscription cancellation
    const formPayload = {
      ...formData,
      type: FormType.SUBSCRIPTION_CANCELLATION,
      showProgressBar: true,
      allowSaveProgress: false,
      requireAuth: true,
      enableConditionalLogic: false,
    }

    createForm.mutate(formPayload)
  }

  const addQuestion = () => {
    const newQuestion: FormQuestion = {
      id: `question-${Date.now()}`,
      title: "Nouvelle question",
      type: QuestionType.TEXT_SHORT,
      order: formData.questions.length,
      isRequired: false,
    }

    setFormData(prev => ({
      ...prev,
      questions: [...prev.questions, newQuestion],
    }))
  }

  const updateQuestion = (questionId: string, updates: Partial<FormQuestion>) => {
    setFormData(prev => ({
      ...prev,
      questions: prev.questions.map(q =>
        q.id === questionId ? { ...q, ...updates } : q
      ),
    }))
  }

  const removeQuestion = (questionId: string) => {
    setFormData(prev => ({
      ...prev,
      questions: prev.questions.filter(q => q.id !== questionId),
    }))
  }

  // Option management functions
  const addOption = (questionId: string) => {
    const newOption = {
      id: `option-${Date.now()}`,
      label: "Nouvelle option",
      value: `option-${Date.now()}`,
    }

    updateQuestion(questionId, {
      options: [...(getQuestion(questionId)?.options || []), newOption],
    })
  }

  const updateOption = (questionId: string, optionId: string, updates: Partial<{ label: string; value: string }>) => {
    const question = getQuestion(questionId)
    if (!question?.options) return

    const updatedOptions = question.options.map(option =>
      option.id === optionId ? { ...option, ...updates } : option
    )

    updateQuestion(questionId, { options: updatedOptions })
  }

  const removeOption = (questionId: string, optionId: string) => {
    const question = getQuestion(questionId)
    if (!question?.options) return

    const updatedOptions = question.options.filter(option => option.id !== optionId)
    updateQuestion(questionId, { options: updatedOptions })
  }

  const getQuestion = (questionId: string) => {
    return formData.questions.find(q => q.id === questionId)
  }

  // Validation for choice questions
  const validateQuestion = (question: FormQuestion): string | null => {
    if (question.type === QuestionType.SINGLE_CHOICE || question.type === QuestionType.MULTIPLE_CHOICE) {
      if (!question.options || question.options.length < 2) {
        return "Les questions à choix doivent avoir au moins 2 options"
      }
    }
    return null
  }

  const getQuestionTypeLabel = (type: QuestionType) => {
    switch (type) {
      case QuestionType.TEXT_SHORT:
        return "Texte court"
      case QuestionType.TEXT_LONG:
        return "Texte long"
      case QuestionType.EMAIL:
        return "Email"
      case QuestionType.NUMBER:
        return "Nombre"
      case QuestionType.RATING:
        return "Note"
      case QuestionType.YES_NO:
        return "Oui/Non"
      case QuestionType.SINGLE_CHOICE:
        return "Choix unique"
      case QuestionType.MULTIPLE_CHOICE:
        return "Choix multiple"
      case QuestionType.DATE:
        return "Date"
      default:
        return type
    }
  }

  if (isPreviewMode) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Button
            variant="light"
            startContent={<ArrowLeft className="size-4" />}
            onPress={() => setIsPreviewMode(false)}
          >
            Retour à l&apos;édition
          </Button>
        </div>

        <Card>
          <CardHeader>
            <h3 className="text-xl font-semibold">{formData.title}</h3>
            {formData.description && (
              <p className="text-default-600">{formData.description}</p>
            )}
          </CardHeader>
          <CardBody className="space-y-6">
            {formData.questions.map((question) => (
              <div key={question.id} className="space-y-2">
                <h4 className="font-medium flex items-center gap-2">
                  {question.title}
                  {question.isRequired && (
                    <span className="text-danger text-sm">*</span>
                  )}
                </h4>
                {question.description && (
                  <p className="text-sm text-default-600">{question.description}</p>
                )}
                <div className="p-4 bg-default-100 rounded-lg">
                  <p className="text-sm text-default-500">
                    Type: {getQuestionTypeLabel(question.type)}
                  </p>
                </div>
              </div>
            ))}
          </CardBody>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <Button
          variant="light"
          startContent={<ArrowLeft className="size-4" />}
          onPress={() => router.push("/admin/forms")}
        >
          Retour aux formulaires
        </Button>

        <div className="flex items-center gap-2">
          <Button
            variant="light"
            startContent={<Eye className="size-4" />}
            onPress={() => setIsPreviewMode(true)}
          >
            Aperçu
          </Button>
          <Button
            color="primary"
            startContent={<Save className="size-4" />}
            onPress={handleSave}
            isLoading={createForm.isPending}
          >
            Enregistrer
          </Button>
        </div>
      </div>

      {/* Form Configuration */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Configuration du formulaire</h3>
        </CardHeader>
        <CardBody className="space-y-4">
          <Input
            label="Titre du formulaire"
            placeholder="Entrez le titre du formulaire d'annulation..."
            value={formData.title}
            onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
            isRequired
            description="Ce formulaire sera présenté aux utilisateurs lors de l'annulation de leur abonnement"
          />

          <Textarea
            label="Description (optionnelle)"
            placeholder="Expliquez pourquoi vous collectez ces informations..."
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            description="Cette description sera affichée en haut du formulaire"
          />
        </CardBody>
      </Card>

      {/* Questions */}
      <Card>
        <CardHeader className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">Questions</h3>
          <Button
            color="primary"
            size="sm"
            startContent={<Plus className="size-4" />}
            onPress={addQuestion}
          >
            Ajouter une question
          </Button>
        </CardHeader>
        <CardBody className="space-y-4">
          {formData.questions.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-default-500">Aucune question ajoutée</p>
              <Button
                color="primary"
                variant="light"
                onPress={addQuestion}
                className="mt-2"
              >
                Ajouter votre première question
              </Button>
            </div>
          ) : (
            formData.questions.map((question, index) => (
              <motion.div
                key={question.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-4 border border-default-200 rounded-lg space-y-4"
              >
                <div className="flex items-center justify-between">
                  <span className="text-sm text-default-500">Question {index + 1}</span>
                  <Button
                    size="sm"
                    color="danger"
                    variant="light"
                    onPress={() => removeQuestion(question.id)}
                  >
                    Supprimer
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Titre de la question"
                    value={question.title}
                    onChange={(e) => updateQuestion(question.id, { title: e.target.value })}
                  />

                  <Select
                    label="Type de question"
                    selectedKeys={[question.type]}
                    onSelectionChange={(keys) =>
                      updateQuestion(question.id, {
                        type: Array.from(keys)[0] as QuestionType
                      })
                    }
                  >
                    {Object.values(QuestionType).map(type => (
                      <SelectItem key={type}>
                        {getQuestionTypeLabel(type)}
                      </SelectItem>
                    ))}
                  </Select>
                </div>

                <Textarea
                  label="Description (optionnelle)"
                  value={question.description || ""}
                  onChange={(e) => updateQuestion(question.id, { description: e.target.value })}
                />

                {/* Validation Rules */}
                <div className="space-y-4">
                  <Switch
                    isSelected={question.isRequired}
                    onValueChange={(checked) =>
                      updateQuestion(question.id, { isRequired: checked })
                    }
                  >
                    Question obligatoire
                  </Switch>

                  {/* Text validation rules */}
                  {(question.type === QuestionType.TEXT_SHORT || question.type === QuestionType.TEXT_LONG) && (
                    <div className="grid grid-cols-2 gap-4">
                      <Input
                        type="number"
                        label="Longueur minimale"
                        placeholder="0"
                        value={question.minLength?.toString() || ""}
                        onChange={(e) => {
                          const value = parseInt(e.target.value) || undefined
                          updateQuestion(question.id, { minLength: value })
                        }}
                      />
                      <Input
                        type="number"
                        label="Longueur maximale"
                        placeholder="500"
                        value={question.maxLength?.toString() || ""}
                        onChange={(e) => {
                          const value = parseInt(e.target.value) || undefined
                          updateQuestion(question.id, { maxLength: value })
                        }}
                      />
                    </div>
                  )}

                  {/* Number/Rating validation rules */}
                  {(question.type === QuestionType.NUMBER || question.type === QuestionType.RATING) && (
                    <div className="grid grid-cols-2 gap-4">
                      <Input
                        type="number"
                        label="Valeur minimale"
                        placeholder={question.type === QuestionType.RATING ? "1" : "0"}
                        value={question.minValue?.toString() || ""}
                        onChange={(e) => {
                          const value = parseFloat(e.target.value) || undefined
                          updateQuestion(question.id, { minValue: value })
                        }}
                      />
                      <Input
                        type="number"
                        label="Valeur maximale"
                        placeholder={question.type === QuestionType.RATING ? "5" : "100"}
                        value={question.maxValue?.toString() || ""}
                        onChange={(e) => {
                          const value = parseFloat(e.target.value) || undefined
                          updateQuestion(question.id, { maxValue: value })
                        }}
                      />
                    </div>
                  )}

                  {/* Options for choice questions */}
                  {(question.type === QuestionType.SINGLE_CHOICE || question.type === QuestionType.MULTIPLE_CHOICE) && (
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <label className="text-sm font-medium">Options de réponse</label>
                        <Button
                          size="sm"
                          color="primary"
                          variant="light"
                          startContent={<Plus className="size-4" />}
                          onPress={() => addOption(question.id)}
                        >
                          Ajouter une option
                        </Button>
                      </div>

                      {question.options && question.options.length > 0 ? (
                        <div className="space-y-2">
                          {question.options.map((option) => (
                            <div key={option.id} className="flex items-center gap-2">
                              <div className="flex-1 grid grid-cols-2 gap-2">
                                <Input
                                  size="sm"
                                  placeholder="Texte affiché"
                                  value={option.label}
                                  onChange={(e) => updateOption(question.id, option.id, { label: e.target.value })}
                                />
                                <Input
                                  size="sm"
                                  placeholder="Valeur (optionnel)"
                                  value={option.value}
                                  onChange={(e) => updateOption(question.id, option.id, { value: e.target.value })}
                                />
                              </div>
                              <Button
                                size="sm"
                                color="danger"
                                variant="light"
                                isIconOnly
                                onPress={() => removeOption(question.id, option.id)}
                                isDisabled={question.options!.length <= 2}
                              >
                                ✕
                              </Button>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-4 border-2 border-dashed border-default-300 rounded-lg">
                          <p className="text-sm text-default-500">Aucune option ajoutée</p>
                          <Button
                            size="sm"
                            color="primary"
                            variant="light"
                            onPress={() => addOption(question.id)}
                            className="mt-2"
                          >
                            Ajouter la première option
                          </Button>
                        </div>
                      )}

                      {question.options && question.options.length < 2 && (
                        <p className="text-sm text-danger">
                          Au moins 2 options sont requises pour les questions à choix
                        </p>
                      )}
                    </div>
                  )}

                  {/* Conditional Logic */}
                  <ConditionalLogicEditor
                    showConditions={question.showConditions}
                    onChange={(conditions) => updateQuestion(question.id, { showConditions: conditions })}
                    availableQuestions={formData.questions.map(q => ({
                      id: q.id,
                      title: q.title,
                      type: q.type,
                      order: q.order,
                    }))}
                    currentQuestionOrder={question.order}
                  />
                </div>
              </motion.div>
            ))
          )}
        </CardBody>
      </Card>
    </div>
  )
}
