// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_PRISMA_URL") // uses connection pooling
  directUrl = env("DATABASE_URL_NON_POOLING") // uses a direct connection
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

enum UserRole {
  ADMIN
  USER
  SAV
  MODO
  IA_BUILDER
}

model User {
  id               String    @id @default(cuid())
  name             String?
  email            String?   @unique
  emailVerified    DateTime?
  profilePictureId String?   @unique
  profilePicture   File?     @relation(fields: [profilePictureId], references: [id], onDelete: SetNull)
  image            String? // Require to use auth.js
  accounts         Account[]

  // Custom fields
  username                   String?                     @unique
  role                       UserRole                    @default(USER) // Legacy role field, kept for backward compatibility
  roles                      UserRole[]                  @default([USER]) // New field for multiple roles
  password                   String?
  hasPassword                Boolean                     @default(false)
  resetPasswordToken         ResetPassordToken?
  userEmailVerificationToken UserEmailVerificationToken?
  lastLocale                 String?
  otpSecret                  String                      @default("")
  otpMnemonic                String                      @default("")
  otpVerified                Boolean                     @default(false)
  uploadsInProgress          FileUploading[]
  chats      Chat[]
  favoriteAgentIds           Int[]
  supportTickets  SupportTicket[]
  supportMessages SupportMessage[]
  mangopayUserId             String?
  mangopayWalletId           String?  // Only for payment receiver

  cards               MangopayCard[]
  subscriptions       Subscription[]
  categorySelection   UserCategorySelection?
  refunds             Refund[]

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}



//? For one time login links
model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([identifier, token])
}

model ResetPassordToken {
  identifier String   @unique
  token      String   @unique
  expires    DateTime
  user       User     @relation(fields: [identifier], references: [id], onDelete: Cascade)
  createdAt  DateTime @default(now())
}

model UserEmailVerificationToken {
  identifier String   @unique
  token      String   @unique
  expires    DateTime
  user       User     @relation(fields: [identifier], references: [id], onDelete: Cascade)
  createdAt  DateTime @default(now())
}

model File {
  id        String   @id @default(cuid())
  key       String   @unique
  filetype  String
  bucket    String
  endpoint  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  userProfilePicture User?
  supportMessage SupportMessage?

  fileUploadingId String?        @unique
  fileUploading   FileUploading? @relation(fields: [fileUploadingId], references: [id], onDelete: SetNull)
}

// Upload in progress
model FileUploading {
  id       String   @id @default(cuid())
  key      String   @unique
  filetype String
  bucket   String
  endpoint String
  expires  DateTime

  file File?

  userId String?
  user   User?   @relation(fields: [userId], references: [id], onDelete: SetNull)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

//Create new models Agent, Badge, Category, and Prompt to implement API functionalities

model Agent {
  id                    Int      @id @default(autoincrement())
  icon                  String
  title                 String
  description           String
  model                 String   @default("gpt-3.5-turbo")
  personality           String   @default("")
  temperature           Int      @default(1)
  additionalInstructions String   @default("")
  skills                Skill[]  @relation("AgentSkills")
  prompts               Prompt[]
  badge                 Badge?   @relation(fields: [badgeId], references: [id])
  badgeId               Int?
  chats      Chat[]
}

model Skill {
  id      Int    @id @default(autoincrement())
  name    String @unique
  agents  Agent[] @relation("AgentSkills") // Many-to-many relationship with Agent
}

model Badge {
  id    Int    @id @default(autoincrement())
  title String
  agents Agent[]
}
model Prompt {
  id          Int       @id @default(autoincrement())
  title       String    @default("")
  body        String
  agentId     Int?
  agent       Agent?    @relation(fields: [agentId], references: [id])
  categoryId  Int?
  category    Category? @relation(fields: [categoryId], references: [id])
}

model Category {
  id      Int    @id @default(autoincrement())
  name    String
  prompts Prompt[]
}

model Chat {
  id          String    @id @default(cuid())
  title       String?
  messages    Message[]
  userId      String    // Reference to the user who owns this chat
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  agentId     Int       // Reference to the agent this chat is with
  agent       Agent     @relation(fields: [agentId], references: [id])
  isSaved     Boolean   @default(false)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

model Message {
  id         String   @id
  content    String   @db.Text
  role       String   // "user" or "assistant" or "system"
  chatId     String
  chat       Chat     @relation(fields: [chatId], references: [id], onDelete: Cascade)
  createdAt  DateTime @default(now())
}

enum TicketStatus {
  OPEN
  IN_PROGRESS
  COMPLETED
}

model SupportTicket {
  id          String         @id @default(cuid())
  title       String
  status      TicketStatus   @default(OPEN)
  userId      String
  user        User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages    SupportMessage[]
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt
}

model SupportMessage {
  id           String         @id @default(cuid())
  content      String
  ticketId     String
  ticket       SupportTicket  @relation(fields: [ticketId], references: [id], onDelete: Cascade)
  senderId     String
  sender       User           @relation(fields: [senderId], references: [id], onDelete: Cascade)
  attachmentId String?        @unique
  attachment   File?          @relation(fields: [attachmentId], references: [id], onDelete: SetNull)
  createdAt    DateTime       @default(now())
}

model MangopayCard {
  id                String    @id @default(cuid())
  mangopayCardId    String?   @unique  // Rendu facultatif pour permettre la création temporaire
  userId            String
  user              User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  isDefault         Boolean   @default(true)
  last4             String?
  expirationDate    String?
  isTemp            Boolean   @default(false)  // Indique si c'est une carte temporaire en attente de l'ID MangoPay
  isActive          Boolean   @default(true)
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
}

enum BillingPeriod {
  MONTHLY
  ANNUAL
}

enum RestrictionType {
  MAX_MESSAGES_PER_CHAT
  MAX_SAVED_CHATS
  MAX_AGENTS
  MAX_CATEGORIES
}

model PlanRestriction {
  id            String          @id @default(cuid())
  type          RestrictionType
  value         Int?
  planId        Int
  plan          Plan            @relation(fields: [planId], references: [id], onDelete: Cascade)
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
}

model Plan {
  id                        Int           @id @default(autoincrement())
  name                      String
  description               String?
  monthlyPrice              Float
  annualPrice               Float
  monthlyRefundPercentage   Int?
  annualRefundPercentage    Int?
  features                  String[]
  isRecommended             Boolean       @default(false)
  isActive                  Boolean       @default(true)
  subscriptions             Subscription[]
  restrictions              PlanRestriction[]
  refunds                   Refund[]
  createdAt                 DateTime      @default(now())
  updatedAt                 DateTime      @updatedAt
}

model UserCategorySelection {
  id                String        @id @default(cuid())
  userId            String        @unique
  user              User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  categoryIds       Int[]          //? Reference to badges
  lastUpdated       DateTime      @default(now())
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt
}

enum SubscriptionStatus {
  ACTIVE
  CANCELED
  EXPIRED
  PENDING
  FAILED
}

model Subscription {
  id                String             @id @default(cuid())
  userId            String
  user              User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  planId            Int
  plan              Plan               @relation(fields: [planId], references: [id])
  status            SubscriptionStatus @default(PENDING)
  billingPeriod     BillingPeriod
  startDate         DateTime
  endDate           DateTime
  canceledAt        DateTime?
  payments          Payment[]
  refunds           Refund[]
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  mangopayRecurringRegistrationId String? @unique
}

enum PaymentStatus {
  PENDING
  SUCCEEDED
  FAILED
  REFUNDED
}

model Payment {
  id                String         @id @default(cuid())
  subscriptionId    String
  subscription      Subscription   @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)
  amount            Float
  currency          String         @default("EUR")
  mangopayPayinId   String?        @unique
  status            PaymentStatus  @default(PENDING)
  failureReason     String?
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt
}

enum RefundStatus {
  PENDING
  COMPLETED
  FAILED
}

model Refund {
  id                String         @id @default(cuid())
  userId            String
  user              User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  planId            Int
  plan              Plan           @relation(fields: [planId], references: [id])
  subscriptionId    String?
  subscription      Subscription?  @relation(fields: [subscriptionId], references: [id], onDelete: SetNull)
  amount            Decimal
  currency          String         @default("EUR")
  mangopayRefundId  String?        @unique
  status            RefundStatus   @default(PENDING)
  failureReason     String?
  processedAt       DateTime?
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt
}
